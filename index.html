<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mantis Clone API Documentation</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
        }
        .container {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 30px;
            max-width: 800px;
            width: 90%;
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 1px solid #eee;
            padding-bottom: 15px;
        }
        .doc-links {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 20px;
        }
        .doc-section {
            flex: 1 1 300px;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 20px;
            margin-bottom: 20px;
        }
        h2 {
            color: #444;
            margin-top: 0;
        }
        ul {
            padding-left: 20px;
        }
        li {
            margin-bottom: 10px;
        }
        a {
            color: #0066cc;
            text-decoration: none;
            font-weight: bold;
            font-size: 18px;
            display: inline-block;
            padding: 10px 20px;
            background-color: #f0f7ff;
            border-radius: 5px;
            margin: 10px;
            transition: background-color 0.3s;
        }
        a:hover {
            background-color: #d0e8ff;
        }
        .language {
            text-align: center;
        }
        .flag {
            font-size: 24px;
            margin-right: 10px;
            vertical-align: middle;
        }
        .frontend-section {
            background-color: #f0f7ff;
            padding: 20px;
            border-radius: 5px;
            margin-top: 20px;
            text-align: center;
        }
        .frontend-link {
            background-color: #1e40af;
            color: white;
            padding: 12px 24px;
            font-size: 18px;
            border-radius: 5px;
            display: inline-block;
            margin-top: 10px;
        }
        .frontend-link:hover {
            background-color: #1e3a8a;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Mantis Clone API Documentation</h1>
        
        <div class="doc-section language">
            <h2>Choose Documentation Language</h2>
            <div class="doc-links">
                <a href="/en/"><span class="flag">🇬🇧</span> English Documentation</a>
                <a href="/et/"><span class="flag">🇪🇪</span> Estonian Documentation</a>
            </div>
        </div>
        
        <div class="doc-section">
            <h2>About this API</h2>
            <p>This API provides access to the Mantis Clone application's backend services. The documentation is available in multiple languages.</p>
            <p>Select your preferred documentation language from the links above.</p>
        </div>

        <div class="frontend-section">
            <h2>Try the Frontend</h2>
            <p>Check out our newly created React frontend for the Mantis Clone API. The frontend provides a user-friendly interface for managing issues, labels, milestones, and comments.</p>
            <a href="/web/" class="frontend-link">Go to Mantis Clone Frontend</a>
            <p class="mt-2 text-sm">The frontend is served from the same server at the /web/ path.</p>
        </div>
    </div>
</body>
</html>