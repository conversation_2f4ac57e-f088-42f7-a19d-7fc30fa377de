const fs = require('fs');

// Read the original file
const apiYaml = fs.readFileSync('api.yaml', 'utf8');

// Replace the profile endpoint with users/me
let updatedYaml = apiYaml.replace('/profile:', '/users/me:');

// Replace the tag and summary
updatedYaml = updatedYaml.replace(
  'tags:\n        - Sessions\n      summary: Get the logged-in user\'s profile',
  'tags:\n        - Users\n      summary: Get the current user\'s profile'
);

// Write the updated content to a new file
fs.writeFileSync('api.yaml.new', updatedYaml);

console.log('API YAML file updated successfully!');
