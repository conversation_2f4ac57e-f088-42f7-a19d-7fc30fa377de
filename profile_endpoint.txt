  /users/me:
    get:
      tags:
        - Users
      summary: Get the current user's profile
      description: Returns the user data from the current session.
      responses:
        '200':
          description: Returns user profile data
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                  user:
                    $ref: '#/components/schemas/GetUser'
        '401':
          description: User is not logged in (no valid session)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
