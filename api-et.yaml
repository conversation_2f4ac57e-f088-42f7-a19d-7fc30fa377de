openapi: 3.0.3
info:
  title: Mantis Clone API (EESTI)
  description: Lihtne ülesannete jälgimise API, mis toetab probleeme, silte, kommentaare ja verstaposte
  version: 1.0.0
  contact:
    name: API Tugi
    email: <EMAIL>

tags:
  - name: Issues
    description: Probleemidega seotud toimingud
  - name: Labels
    description: Siltidega seotud toimingud
  - name: Comments
    description: Kommentaaridega seotud toimingud
  - name: Milestones
    description: Verstapostidega seotud toimingud
  - name: Users
    description: Kasutajate registreerimisega seotud toimingud
  - name: Sessions
    description: Kasutajasessioonide haldamisega seotud toimingud (sisselogimine, väljalogimine, profiil)
  - name: Wildcard
    description: Varuplaan määratlemata teede jaoks

servers:
  - url: /
    description: Tootmisserver
  - url: /
    description: Testserver

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    ###############################
    # Üldine vea skeem
    ###############################
    Error:
      type: object
      properties:
        code:
          type: string
          description: Veakood
        message:
          type: string
          description: Veateade
        details:
          type: object
          description: Täiendavad vea üksikasjad
      required:
        - code
        - message

    ###############################
    # Probleemi skeemid
    ###############################
    CreateIssue:
      type: object
      description: Skeem uue probleemi loomiseks (POST)
      properties:
        title:
          type: string
          maxLength: 200
        description:
          type: string
        status:
          type: string
          enum: [open, in_progress, resolved, closed]
        priority:
          type: string
          enum: [low, medium, high, critical]
        assignee:
          type: string
          format: uuid
        creator:
          type: string
          format: uuid
        labels:
          type: array
          items:
            $ref: '#/components/schemas/GetLabel'
        milestone:
          $ref: '#/components/schemas/GetMilestone'
      required:
        - title
        - status
        - priority
        - creator

    UpdateIssue:
      type: object
      description: Skeem olemasoleva probleemi värskendamiseks (PATCH)
      properties:
        title:
          type: string
          maxLength: 200
        description:
          type: string
        status:
          type: string
          enum: [open, in_progress, resolved, closed]
        priority:
          type: string
          enum: [low, medium, high, critical]
        assignee:
          type: string
          format: uuid
        labels:
          type: array
          items:
            $ref: '#/components/schemas/GetLabel'
        milestone:
          $ref: '#/components/schemas/GetMilestone'
      # ei ole nõutud välju

    GetIssue:
      type: object
      description: Skeem probleemi pärimiseks (GET)
      properties:
        id:
          type: string
          format: uuid
        title:
          type: string
          maxLength: 200
        description:
          type: string
        status:
          type: string
          enum: [open, in_progress, resolved, closed]
        priority:
          type: string
          enum: [low, medium, high, critical]
        assignee:
          type: string
          format: uuid
        creator:
          type: string
          format: uuid
        labels:
          type: array
          items:
            $ref: '#/components/schemas/GetLabel'
        milestone:
          $ref: '#/components/schemas/GetMilestone'
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time

    ###############################
    # Sildi skeemid
    ###############################
    CreateLabel:
      type: object
      description: Skeem uue sildi loomiseks (POST)
      properties:
        name:
          type: string
          maxLength: 50
        color:
          type: string
          pattern: '^#[0-9a-fA-F]{6}$'
        description:
          type: string
          maxLength: 200
      required:
        - name
        - color

    UpdateLabel:
      type: object
      description: Skeem sildi osaliseks värskendamiseks (PATCH)
      properties:
        name:
          type: string
          maxLength: 50
        color:
          type: string
          pattern: '^#[0-9a-fA-F]{6}$'
        description:
          type: string
          maxLength: 200
      # ei ole nõutud välju => osaline

    GetLabel:
      type: object
      description: Skeem olemasoleva sildi pärimiseks
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
          maxLength: 50
        color:
          type: string
          pattern: '^#[0-9a-fA-F]{6}$'
        description:
          type: string
          maxLength: 200

    ###############################
    # Kommentaari skeemid
    ###############################
    CreateComment:
      type: object
      description: Skeem uue kommentaari loomiseks (POST)
      properties:
        issue_id:
          type: string
          format: uuid
        content:
          type: string
        author:
          type: string
          format: uuid
      required:
        - issue_id
        - content
        - author

    UpdateComment:
      type: object
      description: Skeem kommentaari osaliseks värskendamiseks (PATCH)
      properties:
        content:
          type: string
        author:
          type: string
          format: uuid
      # ei ole nõutud välju => osaline

    GetComment:
      type: object
      description: Skeem olemasoleva kommentaari pärimiseks
      properties:
        id:
          type: string
          format: uuid
        issue_id:
          type: string
          format: uuid
        content:
          type: string
        author:
          type: string
          format: uuid
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time

    ###############################
    # Verstaposti skeemid
    ###############################
    CreateMilestone:
      type: object
      description: Skeem uue verstaposti loomiseks (POST)
      properties:
        title:
          type: string
          maxLength: 100
        description:
          type: string
        due_date:
          type: string
          format: date
        status:
          type: string
          enum: [open, closed]
      required:
        - title
        - due_date
        - status
        - description

    UpdateMilestone:
      type: object
      description: Skeem verstaposti osaliseks värskendamiseks (PATCH)
      properties:
        title:
          type: string
          maxLength: 100
        description:
          type: string
        due_date:
          type: string
          format: date
        status:
          type: string
          enum: [open, closed]
      # ei ole nõutud välju => osaline

    GetMilestone:
      type: object
      description: Skeem olemasoleva verstaposti pärimiseks
      properties:
        id:
          type: string
          format: uuid
        title:
          type: string
          maxLength: 100
        description:
          type: string
        due_date:
          type: string
          format: date
        status:
          type: string
          enum: [open, closed]

    ###############################
    # Kasutaja / Autentimise skeemid
    ###############################
    RegisterUser:
      type: object
      description: Skeem kasutaja registreerimiseks (POST /register)
      properties:
        username:
          type: string
        password:
          type: string
      required:
        - username
        - password

    LoginUser:
      type: object
      description: Skeem kasutaja sisselogimiseks (POST /login)
      properties:
        username:
          type: string
        password:
          type: string
      required:
        - username
        - password

    GetUser:
      type: object
      description: Skeem kasutajaprofiili andmete pärimiseks
      properties:
        id:
          type: integer
          description: Automaatselt suurenev kasutaja ID
        username:
          type: string
          description: Unikaalne kasutajanimi

paths:
  #######################
  # Autentimise teed (Registreerimine)
  #######################
  /register:
    post:
      tags:
        - Users
      summary: Registreeri uus kasutaja
      description: Loob uue kasutaja kasutajanime ja parooliga.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RegisterUser'
      responses:
        '201':
          description: Kasutaja registreeritud edukalt
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                  user_id:
                    type: integer
        '400':
          description: Vigane sisend (nt. puudub kasutajanimi või parool)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '409':
          description: Kasutajanimi on juba kasutusel
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Serveri sisemine viga
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  #######################
  # Sessiooni teed (Sisselogimine, Väljalogimine, Profiil)
  #######################
  /login:
    post:
      tags:
        - Sessions
      summary: Logi kasutaja sisse
      description: Autendib kasutaja kasutajanime ja parooliga, seejärel loob sessiooni.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LoginUser'
      responses:
        '200':
          description: Sisselogimine õnnestus
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                  user:
                    $ref: '#/components/schemas/GetUser'
        '400':
          description: Vigane sisend (puudub kasutajanimi või parool)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          description: Vigane kasutajanimi või parool
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Serveri sisemine viga
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /logout:
    delete:
      tags:
        - Sessions
      summary: Logi praegune kasutaja välja
      description: Hävitab praeguse sessiooni.
      responses:
        '200':
          description: Väljalogimine õnnestus
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
        '401':
          description: Kasutaja ei ole sisse logitud
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Serveri sisemine viga
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /profile:
    get:
      tags:
        - Sessions
      summary: Hangi sisselogitud kasutaja profiil
      description: Tagastab kasutaja andmed praegusest sessioonist.
      responses:
        '200':
          description: Tagastab kasutaja profiili andmed
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                  user:
                    $ref: '#/components/schemas/GetUser'
        '401':
          description: Kasutaja ei ole sisse logitud (puudub kehtiv sessioon)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Serveri sisemine viga
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  #######################
  # Probleemid
  #######################
  /issues:
    get:
      tags:
        - Issues
      summary: Kuva kõik probleemid
      parameters:
        - name: status
          in: query
          schema:
            type: string
            enum: [open, in_progress, resolved, closed]
        - name: priority
          in: query
          schema:
            type: string
            enum: [low, medium, high, critical]
        - name: label
          in: query
          schema:
            type: string
        - name: milestone
          in: query
          schema:
            type: string
            format: uuid
        - name: page
          in: query
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: per_page
          in: query
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
      responses:
        '200':
          description: Probleemide nimekiri
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/GetIssue'
                  pagination:
                    type: object
                    properties:
                      total:
                        type: integer
                      page:
                        type: integer
                      per_page:
                        type: integer
        '400':
          description: Vigased parameetrid
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          description: Autoriseerimata
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: Ressurssi ei leitud
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Serveri sisemine viga
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

    post:
      security:
        - bearerAuth: []
      tags:
        - Issues
      summary: Loo uus probleem
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateIssue'
      responses:
        '201':
          description: Probleem loodud edukalt
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetIssue'
        '400':
          description: Vigane sisend
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          description: Autoriseerimata
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Serveri sisemine viga
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /issues/{issueId}:
    parameters:
      - name: issueId
        in: path
        required: true
        schema:
          type: string
          format: uuid

    get:
      tags:
        - Issues
      summary: Hangi probleemi üksikasjad
      responses:
        '200':
          description: Probleemi üksikasjad
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetIssue'
        '404':
          description: Probleemi ei leitud
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Serveri sisemine viga
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

    patch:
      security:
        - bearerAuth: []
      tags:
        - Issues
      summary: Värskenda probleemi
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateIssue'
      responses:
        '200':
          description: Probleem värskendatud edukalt
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetIssue'
        '400':
          description: Vigane sisend
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: Probleemi ei leitud
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Serveri sisemine viga
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

    delete:
      security:
        - bearerAuth: []
      tags:
        - Issues
      summary: Kustuta probleem
      responses:
        '204':
          description: Probleem kustutatud edukalt
        '404':
          description: Probleemi ei leitud
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Serveri sisemine viga
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /issues/{issueId}/comments:
    parameters:
      - name: issueId
        in: path
        required: true
        schema:
          type: string
          format: uuid

    get:
      tags:
        - Comments
      summary: Kuva probleemile lisatud kommentaarid
      responses:
        '200':
          description: Kommentaaride nimekiri
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/GetComment'
        '404':
          description: Probleemi ei leitud
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Serveri sisemine viga
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

    post:
      security:
        - bearerAuth: []
      tags:
        - Comments
      summary: Lisa kommentaar probleemile
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateComment'
      responses:
        '201':
          description: Kommentaar lisatud edukalt
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetComment'
        '400':
          description: Vigane sisend
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: Probleemi ei leitud
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Serveri sisemine viga
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  #######################
  # Kommentaarid: eraldi tee patch/delete jaoks
  #######################
  /comments/{commentId}:
    parameters:
      - name: commentId
        in: path
        required: true
        schema:
          type: string
          format: uuid
    patch:
      security:
        - bearerAuth: []
      tags:
        - Comments
      summary: Värskenda kommentaari
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateComment'
      responses:
        '200':
          description: Kommentaar värskendatud edukalt
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetComment'
        '400':
          description: Vigane sisend
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: Kommentaari ei leitud
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Serveri sisemine viga
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

    delete:
      security:
        - bearerAuth: []
      tags:
        - Comments
      summary: Kustuta kommentaar
      responses:
        '204':
          description: Kommentaar kustutatud edukalt
        '404':
          description: Kommentaari ei leitud
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Serveri sisemine viga
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  #######################
  # Sildid
  #######################
  /labels:
    get:
      tags:
        - Labels
      summary: Kuva kõik sildid
      responses:
        '200':
          description: Siltide nimekiri
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/GetLabel'
        '500':
          description: Serveri sisemine viga
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

    post:
      security:
        - bearerAuth: []
      tags:
        - Labels
      summary: Loo uus silt
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateLabel'
      responses:
        '201':
          description: Silt loodud edukalt
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetLabel'
        '400':
          description: Vigane sisend
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Serveri sisemine viga
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /labels/{labelId}:
    parameters:
      - name: labelId
        in: path
        required: true
        schema:
          type: string
          format: uuid
    patch:
      security:
        - bearerAuth: []
      tags:
        - Labels
      summary: Värskenda olemasolevat silti
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateLabel'
      responses:
        '200':
          description: Silt värskendatud edukalt
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetLabel'
        '400':
          description: Vigane sisend
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: Silti ei leitud
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Serveri sisemine viga
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

    delete:
      security:
        - bearerAuth: []
      tags:
        - Labels
      summary: Kustuta silt
      responses:
        '204':
          description: Silt kustutatud edukalt
        '404':
          description: Silti ei leitud
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Serveri sisemine viga
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  #######################
  # Verstapostid
  #######################
  /milestones:
    get:
      tags:
        - Milestones
      summary: Kuva kõik verstapostid
      parameters:
        - name: status
          in: query
          schema:
            type: string
            enum: [open, closed]
      responses:
        '200':
          description: Verstapostide nimekiri
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/GetMilestone'
        '500':
          description: Serveri sisemine viga
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

    post:
      security:
        - bearerAuth: []
      tags:
        - Milestones
      summary: Loo uus verstapost
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateMilestone'
      responses:
        '201':
          description: Verstapost loodud edukalt
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetMilestone'
        '400':
          description: Vigane sisend
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Serveri sisemine viga
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /milestones/{milestoneId}:
    parameters:
      - name: milestoneId
        in: path
        required: true
        schema:
          type: string
          format: uuid
    patch:
      security:
        - bearerAuth: []
      tags:
        - Milestones
      summary: Värskenda olemasolevat verstapostit
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateMilestone'
      responses:
        '200':
          description: Verstapost värskendatud edukalt
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetMilestone'
        '400':
          description: Vigane sisend
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: Verstapostit ei leitud
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Serveri sisemine viga
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

    delete:
      security:
        - bearerAuth: []
      tags:
        - Milestones
      summary: Kustuta verstapost
      responses:
        '204':
          description: Verstapost kustutatud edukalt
        '404':
          description: Verstapostit ei leitud
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Serveri sisemine viga
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  #######################
  # Varuplaan marsruut
  #######################
  '/{Wildcard*}':
    get:
      tags:
        - Wildcard
      summary: Varuplaan määratlemata marsruutide jaoks
      description: Tagastab 404 vea iga marsruudi jaoks, mis pole konkreetselt määratletud.
      parameters:
        - name: wildcard
          in: path
          required: true
          schema:
            type: string
      responses:
        '404':
          description: See marsruut tagastab alati vastuse "Ei leitud"
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

security:
  - bearerAuth: []
