@echo off
echo Resolving Git conflicts and pushing changes...

cd C:\Users\<USER>\WebstormProjects\mantis-clone-api

echo Current status:
git status

echo.
echo Stashing your local changes...
git stash

echo.
echo Pulling latest changes from remote...
git pull origin main

echo.
echo Applying your stashed changes...
git stash pop

echo.
echo Adding modified files...
git add app.js api.yaml

echo.
echo Committing changes...
git commit -m "Refactor endpoints to follow RESTful design principles: Change POST /register to POST /users, POST /login to POST /sessions, DELETE /logout to DELETE /sessions/{sessionId}, GET /profile to GET /users/me"

echo.
echo Pushing to remote repository...
git push origin main

echo.
echo Done!
pause
