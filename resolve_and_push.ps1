# Resolving Git conflicts and pushing changes
Write-Host "Resolving Git conflicts and pushing changes..." -ForegroundColor Green

Set-Location -Path "C:\Users\<USER>\WebstormProjects\mantis-clone-api"

Write-Host "`nCurrent status:" -ForegroundColor Yellow
git status

Write-Host "`nStashing your local changes..." -ForegroundColor Yellow
git stash

Write-Host "`nPulling latest changes from remote..." -ForegroundColor Yellow
git pull origin main

Write-Host "`nApplying your stashed changes..." -ForegroundColor Yellow
git stash pop

Write-Host "`nAdding modified files..." -ForegroundColor Yellow
git add app.js api.yaml

Write-Host "`nCommitting changes..." -ForegroundColor Yellow
git commit -m "Refactor endpoints to follow RESTful design principles: Change POST /register to POST /users, POST /login to POST /sessions, DELETE /logout to DELETE /sessions/{sessionId}, GET /profile to GET /users/me"

Write-Host "`nPushing to remote repository..." -ForegroundColor Yellow
git push origin main

Write-Host "`nDone!" -ForegroundColor Green
Read-Host -Prompt "Press Enter to exit"
